/* New styles for Topic Card Layout */
.topics-list-container {
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  padding: 0 16px;
}

.topic-card {
  background-color: #FFFFFF;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
}

.topic-card:hover {
  border-color: #c0c0c0;
  box-shadow: 0 6px 16px rgba(0,0,0,0.08);
}

.topic-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  cursor: pointer;
}

.topic-card-title h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.topic-card-title p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.topic-card-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.topic-expand-btn {
  background: none;
  border: none;
  cursor: pointer;
}

.expand-icon {
  transition: transform 0.3s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.topic-card-body {
  padding: 0 24px 24px 24px;
  border-top: 1px solid #e0e0e0;
}

.resource-section {
  margin-top: 20px;
}

.resource-section h4 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}

.resource-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 8px;
  background-color: #f9f9f9;
  margin-bottom: 8px;
}

.add-resource-btn {
  margin-top: 12px;
  background-color: #f1f1f1;
  color: #333;
  border: 1px solid #ccc;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
}

.add-resource-btn:hover {
  background-color: #e0e0e0;
}
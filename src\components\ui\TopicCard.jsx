import React, { useState } from 'react';

const TopicCard = ({ topic, onEdit, onDelete }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => setIsExpanded(!isExpanded);
  
  // Placeholder functions for file uploads
  const handleAddPdf = () => alert(`Add PDF for ${topic.title}`);
  const handleAddAudio = () => alert(`Add Audio for ${topic.title}`);

  const statusClassName = {
    active: 'status-active',
    draft: 'status-draft',
    inactive: 'status-inactive',
  }[topic.status] || 'status-draft';

  return (
    <div className="topic-card">
      <div className="topic-card-header" onClick={toggleExpand}>
        <div className="topic-card-title">
          <h3>{topic.title}</h3>
          <p>{topic.description}</p>
        </div>
        <div className="topic-card-actions">
          <span className={`status-badge ${statusClassName}`}>{topic.status}</span>
          <button className="topic-edit-btn" onClick={(e) => { e.stopPropagation(); onEdit(topic.id); }}>
            {/* SVG for Edit */}
          </button>
          <button className="topic-delete-btn" onClick={(e) => { e.stopPropagation(); onDelete(topic.id); }}>
             {/* SVG for Delete */}
          </button>
          <button className="topic-expand-btn">
            <svg className={`expand-icon ${isExpanded ? 'expanded' : ''}`} width="20" height="20" viewBox="0 0 24 24">{/* SVG for expand/collapse */}</svg>
          </button>
        </div>
      </div>
      
      {isExpanded && (
        <div className="topic-card-body">
          <div className="resource-section">
            <h4>PDF Resources</h4>
            {topic.resources.pdfs.length > 0 ? (
              topic.resources.pdfs.map((pdf, index) => (
                <div key={index} className="resource-item">
                   {/* PDF Icon, name, size, date */}
                </div>
              ))
            ) : <p>No PDF resources available.</p>}
            <button className="add-resource-btn" onClick={handleAddPdf}>Add PDF</button>
          </div>
          
          <div className="resource-section">
            <h4>Audio Resources</h4>
            {topic.resources.audios.length > 0 ? (
              topic.resources.audios.map((audio, index) => (
                <div key={index} className="resource-item">
                  {/* Audio Icon, name, size, date */}
                </div>
              ))
            ) : <p>No audio resources available.</p>}
            <button className="add-resource-btn" onClick={handleAddAudio}>Add Audio</button>
          </div>
        </div>
      )}
    </div>
  );
};

export default TopicCard;
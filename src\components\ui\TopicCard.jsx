import React, { useState } from 'react';

const TopicCard = ({ topic, onEdit, onDelete }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => setIsExpanded(!isExpanded);

  // Placeholder functions for file uploads
  const handleAddPdf = () => alert(`Add PDF for ${topic.title}`);
  const handleAddAudio = () => alert(`Add Audio for ${topic.title}`);

  const statusClassName = {
    active: 'status-active',
    draft: 'status-draft',
    inactive: 'status-inactive',
  }[topic.status] || 'status-draft';

  return (
    <div className="topic-card">
      <div className="topic-card-header" onClick={toggleExpand}>
        <div className="topic-card-title">
          <h3>{topic.title}</h3>
          <p>{topic.description}</p>
        </div>
        <div className="topic-card-actions">
          <span className={`status-badge ${statusClassName}`}>{topic.status}</span>
          <button className="topic-edit-btn" onClick={(e) => { e.stopPropagation(); onEdit(topic.id); }}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
              <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
            </svg>
          </button>
          <button className="topic-delete-btn" onClick={(e) => { e.stopPropagation(); onDelete(topic.id); }}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="3,6 5,6 21,6"/>
              <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
              <line x1="10" y1="11" x2="10" y2="17"/>
              <line x1="14" y1="11" x2="14" y2="17"/>
            </svg>
          </button>
          <button className="topic-expand-btn">
            <svg className={`expand-icon ${isExpanded ? 'expanded' : ''}`} width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="6,9 12,15 18,9"/>
            </svg>
          </button>
        </div>
      </div>

      {isExpanded && (
        <div className="topic-card-body">
          <div className="resource-section">
            <h4>PDF Resources</h4>
            {topic.resources.pdfs.length > 0 ? (
              topic.resources.pdfs.map((pdf, index) => (
                <div key={index} className="resource-item">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{marginRight: '8px', color: '#dc3545'}}>
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2Z"/>
                    <polyline points="14,2 14,8 20,8"/>
                    <line x1="16" y1="13" x2="8" y2="13"/>
                    <line x1="16" y1="17" x2="8" y2="17"/>
                    <polyline points="10,9 9,9 8,9"/>
                  </svg>
                  <div style={{flex: 1}}>
                    <div style={{fontWeight: '500', fontSize: '14px'}}>{pdf.name}</div>
                    <div style={{fontSize: '12px', color: '#666'}}>{pdf.size} • {pdf.date}</div>
                  </div>
                </div>
              ))
            ) : <p>No PDF resources available.</p>}
            <button className="add-resource-btn" onClick={handleAddPdf}>Add PDF</button>
          </div>

          <div className="resource-section">
            <h4>Audio Resources</h4>
            {topic.resources.audios.length > 0 ? (
              topic.resources.audios.map((audio, index) => (
                <div key={index} className="resource-item">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{marginRight: '8px', color: '#28a745'}}>
                    <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"/>
                    <path d="m19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"/>
                  </svg>
                  <div style={{flex: 1}}>
                    <div style={{fontWeight: '500', fontSize: '14px'}}>{audio.name}</div>
                    <div style={{fontSize: '12px', color: '#666'}}>{audio.size} • {audio.date}</div>
                  </div>
                </div>
              ))
            ) : <p>No audio resources available.</p>}
            <button className="add-resource-btn" onClick={handleAddAudio}>Add Audio</button>
          </div>
        </div>
      )}
    </div>
  );
};

export default TopicCard;